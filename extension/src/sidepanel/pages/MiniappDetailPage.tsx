import { useCallback, useEffect, useRef, useState } from 'react';
import { Tooltip } from 'antd';
import { X } from 'lucide-react';
import historyIcon from '~/assets/icons/history.svg';
import { useLiveQuery } from 'dexie-react-hooks';
import { ChatStatus } from '@the-agent/shared';

import { InputArea } from '../components';
import { useNavigate, useParams } from 'react-router-dom';
import { formatDate, getAvatarColor, getInitials } from '~/utils/profile';
import MessageList from '../components/MessageList';
import { ChatHandler } from '~/chat/handler';
import { getMiniapp } from '~/services/miniapp';
import { useLanguage } from '~/utils/i18n';

export const MiniappDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { getMessage } = useLanguage();
  const miniapp = useLiveQuery(() => getMiniapp(Number(id)), [id]);
  const [prompt, setPrompt] = useState('');
  const [status, setStatus] = useState<ChatStatus>('idle');
  const [chatHandler, setChatHandler] = useState<ChatHandler | null>(null);

  const [workflowMode, setWorkflowMode] = useState(false);
  const [showHistoricalVersions, setShowHistoricalVersions] = useState(false);

  // measure the height of the info + optimized versions list
  const topBlockRef = useRef<HTMLDivElement | null>(null);
  const [topOffset, setTopOffset] = useState(0);
  const HEADER_HEIGHT = 44;

  const handleBack = () => {
    if (showHistoricalVersions) {
      setShowHistoricalVersions(false);
    } else {
      navigate('/miniapps');
    }
  };

  useEffect(() => {
    if (miniapp?.conversation_id !== -1) {
      setChatHandler(
        new ChatHandler({
          currentConversationId: miniapp?.conversation_id ?? 0,
          workflowMode: workflowMode,
          conversationType: 'miniapp',
          setStatus,
        })
      );
    }
  }, [miniapp?.conversation_id, workflowMode, setStatus]);

  const abort = useCallback(() => {
    chatHandler?.abort();
  }, [chatHandler]);

  const welcomeComponent = () => {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          gap: '16px',
        }}
      >
        <div
          style={{
            textAlign: 'center',
            maxWidth: '400px',
          }}
        >
          <h3
            style={{
              fontSize: '24px',
              fontWeight: 600,
              color: '#111827',
              marginBottom: '8px',
            }}
          >
            {getMessage('miniappDetailHello')}
          </h3>
          <h4
            style={{
              fontSize: '20px',
              fontWeight: 500,
              color: '#111827',
              marginBottom: '16px',
            }}
          >
            {getMessage('miniappDetailImAssistant')}
          </h4>
          <p
            style={{
              fontSize: '16px',
              color: '#6b7280',
              lineHeight: 1.5,
            }}
          >
            {getMessage('miniappDetailTellMe')}
          </p>
        </div>
      </div>
    );
  };

  // measure the combined height of the info section and the optimized versions list
  useEffect(() => {
    const element = topBlockRef.current;
    if (!element) return;

    const updateHeight = () => {
      const measured = element.getBoundingClientRect().height;
      setTopOffset(HEADER_HEIGHT + measured);
    };

    updateHeight();

    const resizeObserver = new ResizeObserver(() => updateHeight());
    resizeObserver.observe(element);
    return () => resizeObserver.disconnect();
  }, [miniapp?.id, miniapp?.history?.length]);

  if (!miniapp || miniapp.id === -1) {
    return <div>{getMessage('miniappDetailNotFound')}</div>;
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100vh', width: '100%' }}>
      {/* Header */}
      <div style={{ position: 'sticky', top: 0, zIndex: 10, backgroundColor: '#ffffff' }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: '#ffffff',
            padding: '0 16px',
            height: '44px',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <h2 style={{ fontSize: '15px', fontWeight: 600, color: '#111827' }}>
            {showHistoricalVersions
              ? getMessage('miniappDetailHeaderHistory')
              : getMessage('miniappDetailHeaderDevelop')}
          </h2>
          <Tooltip title={getMessage('tooltipClose')} placement="bottom">
            <button
              onClick={handleBack}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: 'transparent',
                border: 'none',
                color: '#6b7280',
                cursor: 'pointer',
                transition: 'background 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#E5E7EB';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <X color="#374151" size={20} />
            </button>
          </Tooltip>
        </div>
      </div>

      {/* Content */}
      <div style={{ display: 'flex', flexDirection: 'column', flex: 1, overflow: 'hidden' }}>
        {/* Display miniapp info at top + Optimized versions list (measured area) */}
        <div ref={topBlockRef}>
          {/* Display miniapp info at top */}
          <div style={{ padding: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div
                style={{
                  width: '48px',
                  height: '48px',
                  borderRadius: '12px',
                  backgroundColor: getAvatarColor(miniapp.name),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#ffffff',
                  fontSize: '14px',
                  fontWeight: 600,
                  flexShrink: 0,
                }}
              >
                {getInitials(miniapp.name)}
              </div>
              <div style={{ flex: 1, minWidth: 0 }}>
                <div
                  style={{
                    fontSize: '16px',
                    fontWeight: 600,
                    color: '#111827',
                    marginBottom: '2px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {miniapp.name}
                </div>
                <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '2px' }}>
                  {getMessage('miniappDetailVersion', String(miniapp?.history?.length ?? '1'))}
                </div>
                <div style={{ fontSize: '12px', color: '#6b7280' }}>
                  {getMessage(
                    'miniappDetailInstalledOn',
                    formatDate(miniapp?.installation?.deployed_at || 0)
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Historical versions list - show if miniapp has history and showHistoricalVersions is true */}
          {showHistoricalVersions && miniapp?.history && miniapp?.history.length > 0 && (
            <div
              style={{
                padding: '0px 16px 16px 16px',
                display: 'flex',
                flexDirection: 'column',
                gap: '12px',
              }}
            >
              {miniapp?.history.map((version, index) => (
                <div
                  key={index}
                  style={{
                    padding: '12px',
                    borderRadius: '8px',
                    border: '1px solid #e5e7eb',
                    backgroundColor: '#ffffff',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                  }}
                  onMouseOver={e => {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                    e.currentTarget.style.borderColor = '#d1d5db';
                  }}
                  onMouseOut={e => {
                    e.currentTarget.style.backgroundColor = '#ffffff';
                    e.currentTarget.style.borderColor = '#e5e7eb';
                  }}
                >
                  <div
                    style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '6px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <img
                      src={historyIcon}
                      alt="History"
                      style={{ width: '16px', height: '16px' }}
                    />
                  </div>
                  <div style={{ flex: 1 }}>
                    <div
                      style={{
                        fontSize: '14px',
                        fontWeight: 600,
                        color: '#111827',
                        marginBottom: '2px',
                      }}
                    >
                      {getMessage(
                        'miniappDetailVersion',
                        String((miniapp?.history.length ?? 0) - index)
                      )}
                    </div>
                    <div style={{ fontSize: '12px', color: '#6b7280' }}>
                      {getMessage('miniappDetailGeneratedOn', formatDate(version.deployed_at ?? 0))}
                    </div>
                  </div>
                  <div style={{ color: '#6b7280' }}>
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                    >
                      <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* View historical versions section - Show if miniapp is deployed and showHistoricalVersions is false */}
          {!showHistoricalVersions && miniapp?.installation && (
            <>
              <div style={{ padding: '0px 16px 16px 16px', backgroundColor: '#ffffff' }}>
                {/* TODO add a button 'uninstall' to update the minpapp.installation to null */}
                <button
                  onClick={() => setShowHistoricalVersions(true)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '8px 12px',
                    borderRadius: '8px',
                    border: '1px solid #e5e7eb',
                    backgroundColor: '#ffffff',
                    color: '#374151',
                    fontSize: '14px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    width: '100%',
                    justifyContent: 'space-between',
                  }}
                  onMouseOver={e => {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                    e.currentTarget.style.borderColor = '#d1d5db';
                  }}
                  onMouseOut={e => {
                    e.currentTarget.style.backgroundColor = '#ffffff';
                    e.currentTarget.style.borderColor = '#e5e7eb';
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <img
                      src={historyIcon}
                      alt="History"
                      style={{ width: '16px', height: '16px' }}
                    />
                    <span>{getMessage('miniappDetailViewAllVersions')}</span>
                  </div>
                  <div style={{ color: '#6b7280' }}>
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                    >
                      <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                  </div>
                </button>
              </div>
            </>
          )}
        </div>

        {/* Messages Area (scrolls) */}
        {!showHistoricalVersions && (
          <MessageList
            convId={miniapp?.conversation_id ?? 0}
            workflowMode={workflowMode}
            welcomeComponent={welcomeComponent()}
            status={status}
            hasBanner={false}
            top={topOffset}
            miniapp={miniapp}
          />
        )}
      </div>

      {/* Input Area (bottom) */}
      <InputArea
        prompt={prompt}
        setPrompt={setPrompt}
        onSubmitRich={chatHandler?.handleSubmit}
        status={status}
        abort={abort}
        workflowMode={workflowMode}
        onWorkflowModeChange={setWorkflowMode}
        allowWorkflowMode={false}
      />
    </div>
  );
};
